import sys
import os
import io
import uuid
import json
import traceback
import logging
import soundfile as sf
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta
from typing import Optional, Dict, List
import asyncio
import threading
import tempfile
from utils import *

from pydub import AudioSegment
from fastapi import FastAPI, HTTPException
from fastapi.responses import FileResponse
from pydantic import BaseModel
from piper import PiperVoice
# --- <PERSON><PERSON><PERSON> bảo stdout luôn UTF-8 ---
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')

# --- Load config.json ---
BASE_DIR = Path(__file__).resolve().parent
config_path = BASE_DIR / "config.json"

if not config_path.is_file():
    raise FileNotFoundError(f"Config file not found: {config_path}")

with open(config_path, "r", encoding="utf-8") as f:
    cfg = json.load(f)

# <PERSON><PERSON><PERSON> h<PERSON>nh cơ bản
MODELS_DIR = cfg.get("MODELS_DIR", "models")
OUTPUT_DIR = cfg.get("TTS_OUTPUT_DIR", "output")
RETENTION_DAYS = int(cfg.get("TTS_OUTPUT_RETENTION_DAYS", 7))
PORT = int(cfg.get("PORT", 9003))

# Đảm bảo đường dẫn tuyệt đối
MODELS_DIR = str((BASE_DIR / MODELS_DIR).resolve()) if not os.path.isabs(MODELS_DIR) else MODELS_DIR
OUTPUT_DIR = str((BASE_DIR / OUTPUT_DIR).resolve()) if not os.path.isabs(OUTPUT_DIR) else OUTPUT_DIR
os.makedirs(OUTPUT_DIR, exist_ok=True)

# --- Logger setup ---
def setup_logger(name="log/log-tts-server"):
    now = datetime.now().strftime("%Y-%m-%d-%H-%M-%S")
    log_path = f"{name}-{now}.txt"
    Path(log_path).parent.mkdir(parents=True, exist_ok=True)
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s %(levelname)s [%(filename)s:%(lineno)d] %(message)s",
        handlers=[
            logging.FileHandler(log_path, encoding="utf-8"),
            logging.StreamHandler(),
        ],
    )

setup_logger()

# --- Model Management ---
class ModelInfo:
    def __init__(self, model_id: str, model_path: str, config_path: str, metadata: dict):
        self.model_id = model_id
        self.model_path = model_path
        self.config_path = config_path
        self.metadata = metadata
        self.name = metadata.get("language", {}).get("name_native", model_id)
        self.description = f"{self.name} ({metadata.get('dataset', 'Unknown')})"
        self.language_code = metadata.get("language", {}).get("code", "unknown")
        self.num_speakers = metadata.get("num_speakers", 1)
        self.sample_rate = metadata.get("audio", {}).get("sample_rate", 22050)

class ModelManager:
    def __init__(self, models_dir: str):
        self.models_dir = Path(models_dir)
        self.models: Dict[str, ModelInfo] = {}
        self.piper_voices: Dict[str, PiperVoice] = {}
        self._load_models()

    def _load_models(self):
        """Scan models directory and load model info."""
        if not self.models_dir.exists():
            logging.warning(f"Models directory not found: {self.models_dir}")
            return
        for model_dir in self.models_dir.iterdir():
            if not model_dir.is_dir():
                continue
            try:
                onnx_files = list(model_dir.glob("*.onnx"))
                if not onnx_files:
                    continue
                model_file = onnx_files[0]
                config_file = None
                for config_name in ["config.json", f"{model_dir.name}.json", "metadata.json",f'{onnx_files}.json']:
                    candidate = model_dir / config_name
                    if candidate.exists():
                        config_file = candidate
                        break
                if config_file:
                    with open(config_file, "r", encoding="utf-8") as f:
                        metadata = json.load(f)
                else:
                    metadata = {
                        "language": {
                            "code": "vi_VN",
                            "name_native": model_dir.name,
                            "name_english": "Vietnamese Voice"
                        },
                        "dataset": "unknown",
                        "num_speakers": 1,
                        "audio": {"sample_rate": 22050}
                    }
                self.models[model_dir.name] = ModelInfo(
                    model_id=model_dir.name,
                    model_path=str(model_file),
                    config_path=str(config_file) if config_file else "",
                    metadata=metadata
                )
            except Exception as e:
                logging.error(f"Failed to load model {model_dir.name}: {e}")

    def get_model_list(self) -> List[dict]:
        return [
            {
                "model_id": model_id,
                "name": model_info.name,
                "description": model_info.description,
                "language_code": model_info.language_code,
                "dataset": model_info.metadata.get("dataset", "Unknown"),
                "num_speakers": model_info.num_speakers,
                "sample_rate": model_info.sample_rate
            }
            for model_id, model_info in self.models.items()
        ]

    def get_piper_voice(self, model_id: str) -> PiperVoice:
        if model_id not in self.models:
            raise ValueError(f"Model not found: {model_id}")
        if model_id not in self.piper_voices:
            model_info = self.models[model_id]
            config_path = model_info.config_path if model_info.config_path else None
            self.piper_voices[model_id] = PiperVoice.load(
                model_path=model_info.model_path,
                config_path=config_path
            )
        return self.piper_voices[model_id]

# --- Khởi tạo Model Manager ---
model_manager = ModelManager(MODELS_DIR)

# --- FastAPI app ---
app = FastAPI(title="Multi-Model Piper TTS Server", version="2.0.0")

# --- Models ---
class TtsRequest(BaseModel):
    text: str
    model_id: Optional[str] = None  # Nếu None, sẽ dùng model đầu tiên
    length_scale: Optional[float] = 1.0  # Tốc độ nói (Piper parameter)
    noise_scale: Optional[float] = 0.667  # Noise level  
    noise_w: Optional[float] = 0.8  # Noise width

class TtsResponse(BaseModel):
    message: str
    file: str
    model_used: Optional[str] = None

class ModelListResponse(BaseModel):
    models: List[dict]

# ---- Cleanup functions ----
_cleanup_lock = threading.Lock()

def _safe_inside_dir(path: Path, base: Path) -> bool:
    try:
        return base in path.resolve().parents or path.resolve() == base.resolve()
    except Exception:
        return False

def cleanup_old_files(directory: str, keep_days: int, now: Optional[datetime] = None) -> int:
    base = Path(directory).resolve()
    if not base.exists():
        return 0
    now = now or datetime.now()
    cutoff = now - timedelta(days=keep_days)
    deleted = 0
    for p in base.iterdir():
        if p.is_file():
            if not _safe_inside_dir(p, base):
                continue
            try:
                mtime = datetime.fromtimestamp(p.stat().st_mtime)
                if mtime < cutoff:
                    p.unlink(missing_ok=True)
                    deleted += 1
            except Exception as e:
                logging.error("Failed to delete %s: %s", str(p), e)
    return deleted

async def trigger_cleanup_background():
    """Chạy dọn file cũ ở background"""
    if _cleanup_lock.locked():
        return

    def _run():
        try:
            deleted = cleanup_old_files(OUTPUT_DIR, RETENTION_DAYS)
            if deleted:
                logging.info(f"[CLEANUP] Deleted {deleted} old file(s) in {OUTPUT_DIR}")
        except Exception:
            logging.error("Cleanup failed:\n%s", traceback.format_exc())

    with _cleanup_lock:
        loop = asyncio.get_running_loop()
        await loop.run_in_executor(None, _run)

# --- Routes ---
@app.get("/")
def read_root():
    return {
        "message": "Multi-Model Piper TTS Server is running",
        "version": "2.0.0",
        "available_models": len(model_manager.models),
    }

@app.get("/models", response_model=ModelListResponse)
def get_models():
    """Lấy danh sách các model có sẵn"""
    return ModelListResponse(models=model_manager.get_model_list())

@app.post("/tts", response_model=TtsResponse)
async def text_to_speech(tts_request: TtsRequest):
    """Chuyển đổi text thành speech với Piper model được chọn"""
    asyncio.create_task(trigger_cleanup_background())
    
    try:
        # ✅ Xác định model sẽ dùng: ưu tiên request, nếu không thì dùng 'giongnam_1', nếu không tồn tại thì dùng model đầu tiên
        if tts_request.model_id:
            model_id = tts_request.model_id
            if model_id not in model_manager.models:
                raise HTTPException(status_code=404, detail=f"Model not found: {model_id}")
        else:
            # Không có model_id → ưu tiên 'giongnam_1'
            if "giongnam_1" in model_manager.models:
                model_id = "giongnam_1"
                logging.info("No model_id provided. Using default model: giongnam_1")
            elif model_manager.models:
                # Fallback: model đầu tiên
                model_id = next(iter(model_manager.models))
                logging.warning(f"No model_id and 'giongnam_1' not found. Falling back to: {model_id}")
            else:
                raise HTTPException(status_code=404, detail="No models available")

        # Lấy PiperVoice instance
        voice = model_manager.get_piper_voice(model_id)
        model_info = model_manager.models[model_id]
        
        # ✅ Điều chỉnh tham số qua voice.config
        voice.config.length_scale = tts_request.length_scale
        voice.config.noise_scale = tts_request.noise_scale
        voice.config.noise_w = tts_request.noise_w
        
        # ✅ Tổng hợp âm thanh
        logging.info(f"Synthesizing text with model {model_id}: {tts_request.text}")
        preprocess_text = preprocess_words(tts_request.text)
        logging.info(f"After normalizer:{preprocess_text}")
        audio_chunks = list(voice.synthesize(preprocess_text))
        
        if not audio_chunks:
            raise ValueError("No audio chunks generated")
        
        # ✅ Ghép audio từ các chunk
        sample_rate = audio_chunks[0].sample_rate
        audio_arrays = [chunk.audio_int16_array for chunk in audio_chunks]
        audio_int16 = np.concatenate(audio_arrays)  # int16 array
        
        # ✅ Lưu file WAV
        file_id = str(uuid.uuid4())
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp_wav:
            wav_path = tmp_wav.name
            sf.write(wav_path, audio_int16.astype(np.float32) / 32768.0, samplerate=sample_rate)
        
        # ✅ Chuyển sang MP3
        mp3_path = os.path.join(OUTPUT_DIR, f"{file_id}.mp3")
        sound = AudioSegment.from_wav(wav_path)
        sound.export(mp3_path, format="mp3")
        os.remove(wav_path)
        
        logging.info(f"TTS generated successfully: {file_id} using model {model_id}")
        
        return TtsResponse(
            message="TTS generation successful", 
            file=file_id,
            model_used=model_id
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logging.error("TTS generation failed:\n%s", traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"TTS generation failed: {str(e)}")


@app.get("/download/{file_id}")
async def download_file(file_id: str):
    """Download file đã generate"""
    file_path = os.path.join(OUTPUT_DIR, f"{file_id}.mp3")
    if os.path.exists(file_path):
        return FileResponse(
            path=file_path, 
            filename=f"{file_id}.mp3", 
            media_type="audio/mpeg"
        )
    raise HTTPException(status_code=404, detail="File not found")

@app.get("/model/{model_id}")
def get_model_info(model_id: str):
    """Lấy thông tin chi tiết của model"""
    if model_id not in model_manager.models:
        raise HTTPException(status_code=404, detail=f"Model not found: {model_id}")
    
    model_info = model_manager.models[model_id]
    return {
        "model_id": model_id,
        "name": model_info.name,
        "description": model_info.description,
        "language_code": model_info.language_code,
        "num_speakers": model_info.num_speakers,
        "sample_rate": model_info.sample_rate,
        "metadata": model_info.metadata
    }

if __name__ == "__main__":
    import uvicorn
    
    # Kiểm tra xem có model nào không
    if not model_manager.models:
        logging.warning("No models found! Please check your models directory.")
    else:
        logging.info(f"Loaded {len(model_manager.models)} Piper model(s)")
        for model_id, model_info in model_manager.models.items():
            logging.info(f"  - {model_id}: {model_info.description}")
    
    uvicorn.run("main:app", host="0.0.0.0", port=PORT, reload=False)


