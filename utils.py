from soe_vinorm import SoeNormalizer
import re

normalizer = SoeNormalizer()


def short_dict():
    d = {}
    with open("custom_words.txt",'r',encoding='utf-8') as f:
        for line in f:
            item = line.split(",")
            d[str(item[0]).lower()] = str(item[1].strip())
    return d


custom_word = short_dict()
exception_words={'AI':'Ây ai','Wi-Fi':'Goai phai','CEO':'<PERSON>ê e ô'}
vietdict = {'b': 'bờ','c':'cờ', 'k': 'cờ', 'tr': 'chờ', 'd': 'dờ', 'đ': 'đờ', 'g': 'gờ', 'l': 'lờ', 'm': 'mờ','f':'phờ',
            'n': 'nờ', 'p': 'pờ', 'ph': 'phờ', 'r': 'rờ', 's': 'xờ', 't': 'tờ', 'th': 'thờ', 'v': 'vờ','x':'sờ'}

def remove_silent_letter(word):
    letters = word.split()[::-1]
    try:
        word = " ".join(letters[next(index for index, value in enumerate(letters) if len(value) > 1 or value not in vietdict):][::-1])
    except:
        word
    return word

def map_customword(word:str,custom_word:dict):
    try:
       return custom_word[word.lower()]
    except:
      return word
    
def process_unvoice(english_word):
    sylable = []
    for i in english_word.split(" "):
        if i in vietdict:
            i = vietdict[i]
        sylable.append(i)
    return " ".join(sylable)

def preprocess_words(text):
    list_word = text.strip().split(" ")
    print(list_word)
    custom_word = short_dict()
    for i in range(len(list_word)):
        if re.match('(?:[a-zA-ZÀ-ỹ]+(?:\s+[a-zA-ZÀ-ỹ]+)*)',list_word[i]):
            list_word[i] = map_customword(list_word[i],custom_word)
            if len(list_word[i]) == 1:
                continue
            if list_word[i] in custom_word.values():
                continue
        list_word[i] = map_customword(list_word[i],custom_word)
    sentence = " ".join(list_word)
    result = normalizer.normalize(sentence)
    print("After normalizer:", result)
    return result

