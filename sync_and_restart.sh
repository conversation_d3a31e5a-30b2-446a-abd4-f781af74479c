#!/usr/bin/env bash
set -e

# Sync and restart script
REMOTE_USER="vetcbot"
REMOTE_HOST="*************"
REMOTE_PATH="/home/<USER>/ServiceRobot/TTS"

echo "🔄 Sync and restart TTS service..."
echo "Remote: ${REMOTE_USER}@${REMOTE_HOST}:${REMOTE_PATH}"
echo "========================================"

# Step 1: Sync files
echo "📁 Step 1: Syncing files..."
SYNC_FILES=(
    "config.json"
    "main.py"
    "requirements.txt"
    "utils.py"
    "dockerfile"
    "run_tts.sh"
)

for file in "${SYNC_FILES[@]}"; do
    if [ -f "$file" ] || [ -d "$file" ]; then
        echo "  ✅ $file"
        scp -r "$file" "${REMOTE_USER}@${REMOTE_HOST}:${REMOTE_PATH}/"
    else
        echo "  ⚠️  $file (not found)"
    fi
done

echo ""
echo "✅ Files synced successfully!"

# Step 2: Restart service on remote
echo ""
echo "🔄 Step 2: Restarting service on remote..."

# Create remote command script
REMOTE_COMMANDS="
cd ${REMOTE_PATH}
echo '🛑 Stopping existing container...'
docker rm -f tts-service 2>/dev/null || true

echo '🔨 Building new image...'
docker build -t tts-service:jetson .

echo '🚀 Starting new container...'
./run_tts.sh

echo '✅ Service restarted successfully!'
echo ''
echo '📋 Service status:'
docker ps | grep tts-service || echo 'Container not running'
echo ''
echo '📝 To view logs:'
echo 'docker logs -f tts-service'
"

# Execute commands on remote
ssh "${REMOTE_USER}@${REMOTE_HOST}" "$REMOTE_COMMANDS"

echo ""
echo "✅ Sync and restart completed!"
echo ""
echo "🔍 Useful commands:"
echo "  ssh ${REMOTE_USER}@${REMOTE_HOST}                    # SSH to remote"
echo "  ssh ${REMOTE_USER}@${REMOTE_HOST} 'docker logs -f tts-service'  # View logs"
echo "  ssh ${REMOTE_USER}@${REMOTE_HOST} 'docker ps'        # Check containers"
