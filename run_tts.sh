#!/usr/bin/env bash
set -e

APP_NAME="tts-service"
IMAGE_NAME="tts-service:jetson"

# Thư mục trên HOST (đổi theo ý bạn)
LOG_DIR="/home/<USER>/ServiceRobot/logs/TTS"
OUT_DIR="/home/<USER>/ServiceRobot/data/TTS_output"
MODELS_HOST_DIR="/home/<USER>/ServiceRobot/TTS/models"   # chứa giongnam_1/, giongnu_1/, ...

# Container paths (khớp với code: MODELS_DIR='models', OUTPUT_DIR='output', logger dùng 'log/')
MODELS_CONT_DIR="/app/models"
OUT_CONT_DIR="/app/output"
LOG_CONT_DIR="/app/log"

# Lấy PORT từ config.json (mặc định 9003)
PORT=$(python - <<'PY'
import json
try:
    with open('config.json','r',encoding='utf-8') as f:
        print(int(json.load(f).get('PORT', 9003)))
except Exception:
    print(9003)
PY
)

echo "📦 Building image $IMAGE_NAME ..."
docker build -t "$IMAGE_NAME" .

# Tạo thư mục host nếu thiếu (nếu bị permission denied, tạo bằng sudo rồi chown lại)
mkdir -p "$LOG_DIR" "$OUT_DIR" "$MODELS_HOST_DIR"

echo "🛑 Stopping old container (if exists) ..."
docker rm -f "$APP_NAME" 2>/dev/null || true

echo "🚀 Running new container on port $PORT (host network) ..."
docker run -d \
  --name "$APP_NAME" \
  --network host \
  --restart unless-stopped \
  -v "$LOG_DIR":"$LOG_CONT_DIR" \
  -v "$OUT_DIR":"$OUT_CONT_DIR" \
  -v "$MODELS_HOST_DIR":"$MODELS_CONT_DIR" \
  "$IMAGE_NAME"

echo "✅ Up. Logs:  docker logs -f $APP_NAME"
echo "🌐 Test:     curl http://127.0.0.1:$PORT/"
echo "📁 Output:   $OUT_DIR (mp3)"
echo "🧠 Models:   $MODELS_HOST_DIR -> $MODELS_CONT_DIR"
