FROM python:3.10-slim

ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1

# pydub cần ffmpeg, soundfile cần libsndfile1
RUN apt-get update && apt-get install -y --no-install-recommends \
    ffmpeg \
    libsndfile1 \
 && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Nếu bạn có requirements.txt, dùng dòng dưới:
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt || true

# Đảm bảo có đủ deps (nếu thiếu trong requirements.txt)
RUN pip install --no-cache-dir \
    fastapi \
    "uvicorn[standard]" \
    pydub \
    numpy \
    soundfile \
    piper-tts soe-vinorm

# Copy code + config + (KHÔNG) copy models để giảm size — models sẽ mount từ host
COPY . .

# Tạo sẵn thư mục log/output (sẽ mount ra host)
RUN mkdir -p /app/log /app/output

# Port mặc định (thực tế sẽ đọc từ main.PORT)
EXPOSE 9003

# Chạy uvicorn và lấy PORT từ main.PORT (đã load từ config.json)
CMD ["python", "-c", "import uvicorn, main; uvicorn.run(main.app, host='0.0.0.0', port=main.PORT)"]
