import logging
import json
import socket
import threading
import asyncio
from datetime import datetime
from pathlib import Path
import uuid
import sys
import os

class JSONFormatter(logging.Formatter):
    """JSON formatter for centralized logging"""
    
    def __init__(self, service_name="ASR-Service"):
        super().__init__()
        self.service_name = service_name
        self.hostname = socket.gethostname()
    
    def format(self, record):
        # Get current task ID for async context
        task_id = self._get_task_id()
        
        # Get request ID from context if available
        request_id = getattr(record, 'request_id', str(uuid.uuid4()))
        
        log_entry = {
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "level": record.levelname,
            "service": self.service_name,
            "request_id": request_id,
            "host": self.hostname,
            "task_id": task_id,
            "message": record.getMessage(),
            "logger": record.name,
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno
        }
        
        # Add exception info if present
        if record.exc_info:
            log_entry["exception"] = self.formatException(record.exc_info)
        
        # Add extra fields if present
        if hasattr(record, 'extra_data'):
            log_entry.update(record.extra_data)
            
        return json.dumps(log_entry, ensure_ascii=False)
    
    def _get_task_id(self):
        """Get current asyncio task ID or thread name"""
        try:
            # Try to get current asyncio task
            task = asyncio.current_task()
            if task:
                return f"task-{id(task)}"
        except RuntimeError:
            pass
        
        # Fallback to thread name
        thread = threading.current_thread()
        return f"thread-{thread.name}"

class ConsoleFormatter(logging.Formatter):
    """Custom console formatter"""
    
    def __init__(self, service_name="RobotApp-Python-Backend"):
        super().__init__()
        self.service_name = service_name
        self.hostname = socket.gethostname()
    
    def format(self, record):
        # Get current task ID for async context
        task_id = self._get_task_id()
        
        # Get request ID from context if available
        request_id = getattr(record, 'request_id', str(uuid.uuid4())[:8])
        
        # Format: [timestamp] LEVEL service request_id [task_id] [host:hostname] - message
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        
        formatted = (
            f"[{timestamp}] {record.levelname:<5} {self.service_name} "
            f"{request_id} [{task_id}] [host:{self.hostname}] - {record.getMessage()}"
        )
        
        # Add exception if present
        if record.exc_info:
            formatted += "\n" + self.formatException(record.exc_info)
            
        return formatted
    
    def _get_task_id(self):
        """Get current asyncio task ID or thread name"""
        try:
            # Try to get current asyncio task
            task = asyncio.current_task()
            if task:
                return f"task-{id(task)}"
        except RuntimeError:
            pass
        
        # Fallback to thread name
        thread = threading.current_thread()
        return thread.name

class ContextLogger:
    """Logger with context support for request_id and extra data"""
    
    def __init__(self, name):
        self.logger = logging.getLogger(name)
        self._context = {}
    
    def set_context(self, **kwargs):
        """Set context data for logging"""
        self._context.update(kwargs)
    
    def clear_context(self):
        """Clear context data"""
        self._context.clear()
    
    def _log_with_context(self, level, msg, *args, **kwargs):
        """Log with context data"""
        extra = kwargs.get('extra', {})
        extra.update(self._context)
        
        # Add request_id to record if in context
        if 'request_id' in self._context:
            extra['request_id'] = self._context['request_id']
        
        kwargs['extra'] = extra
        getattr(self.logger, level)(msg, *args, **kwargs)
    
    def debug(self, msg, *args, **kwargs):
        self._log_with_context('debug', msg, *args, **kwargs)
    
    def info(self, msg, *args, **kwargs):
        self._log_with_context('info', msg, *args, **kwargs)
    
    def warning(self, msg, *args, **kwargs):
        self._log_with_context('warning', msg, *args, **kwargs)
    
    def error(self, msg, *args, **kwargs):
        self._log_with_context('error', msg, *args, **kwargs)
    
    def critical(self, msg, *args, **kwargs):
        self._log_with_context('critical', msg, *args, **kwargs)

def setup_logging(service_name="ASR-Service", log_level="INFO", log_dir="log"):
    """Setup logging with both JSON and console formatters"""
    
    # Create log directory
    Path(log_dir).mkdir(parents=True, exist_ok=True)
    
    # Generate log file names with timestamp
    now = datetime.now().strftime("%Y-%m-%d-%H-%M-%S")
    json_log_file = Path(log_dir) / f"{service_name.lower()}-{now}.json"
    console_log_file = Path(log_dir) / f"{service_name.lower()}-{now}.log"
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level.upper()))
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # JSON file handler for centralized logging
    json_handler = logging.FileHandler(json_log_file, encoding='utf-8')
    json_handler.setFormatter(JSONFormatter(service_name))
    json_handler.setLevel(logging.DEBUG)
    root_logger.addHandler(json_handler)
    
    # Console handler with custom format
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(ConsoleFormatter(service_name))
    console_handler.setLevel(getattr(logging, log_level.upper()))
    root_logger.addHandler(console_handler)
    
    # Optional: Regular log file with console format
    file_handler = logging.FileHandler(console_log_file, encoding='utf-8')
    file_handler.setFormatter(ConsoleFormatter(service_name))
    file_handler.setLevel(logging.DEBUG)
    root_logger.addHandler(file_handler)
    
    return {
        "json_log_file": str(json_log_file),
        "console_log_file": str(console_log_file),
        "service_name": service_name
    }

def get_context_logger(name):
    """Get a context-aware logger"""
    return ContextLogger(name)

# Request ID context manager
class RequestContext:
    """Context manager for request-scoped logging"""
    
    def __init__(self, request_id=None, **extra_data):
        self.request_id = request_id or str(uuid.uuid4())
        self.extra_data = extra_data
        self.logger = get_context_logger("request")
    
    def __enter__(self):
        self.logger.set_context(request_id=self.request_id, **self.extra_data)
        return self.logger
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.logger.clear_context()