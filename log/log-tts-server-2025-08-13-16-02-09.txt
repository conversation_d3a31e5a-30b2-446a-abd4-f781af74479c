2025-08-13 16:02:09,080 INFO [main.py:153] Loaded model: giongnam_1 - <PERSON><PERSON><PERSON><PERSON> (vais1000)
2025-08-13 16:02:09,080 INFO [main.py:153] Loaded model: giongnu_1 - <PERSON><PERSON><PERSON><PERSON> (vais1000)
2025-08-13 16:02:09,105 INFO [main.py:413] Loaded 2 Piper model(s)
2025-08-13 16:02:09,105 INFO [main.py:415]   - giongnam_1: <PERSON><PERSON><PERSON><PERSON> (vais1000)
2025-08-13 16:02:09,105 INFO [main.py:415]   - giongnu_1: <PERSON><PERSON><PERSON><PERSON> (vais1000)
2025-08-13 16:02:09,116 INFO [main.py:153] Loaded model: giongnam_1 - Ti<PERSON><PERSON> V<PERSON> (vais1000)
2025-08-13 16:02:09,117 INFO [main.py:153] Loaded model: giongnu_1 - <PERSON><PERSON><PERSON><PERSON> (vais1000)
2025-08-13 16:02:14,174 INFO [main.py:209] Created PiperVoice instance for model: giongnam_1
2025-08-13 16:02:14,175 ERROR [main.py:370] TTS generation failed:
Traceback (most recent call last):
  File "/Users/<USER>/projects/vetc/robotics/TTS/main.py", line 326, in text_to_speech
    for audio_chunk in voice.synthesize(
TypeError: synthesize() got an unexpected keyword argument 'length_scale'

