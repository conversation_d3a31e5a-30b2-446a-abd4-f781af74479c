2025-08-13 16:58:03,005 INFO [main.py:153] Loaded model: giongnam_1 - Ti<PERSON><PERSON> (vais1000)
2025-08-13 16:58:03,006 INFO [main.py:153] Loaded model: giongnu_1 - <PERSON><PERSON><PERSON><PERSON> (vais1000)
2025-08-13 16:58:03,032 INFO [main.py:413] Loaded 2 Piper model(s)
2025-08-13 16:58:03,032 INFO [main.py:415]   - giongnam_1: <PERSON><PERSON><PERSON><PERSON> (vais1000)
2025-08-13 16:58:03,032 INFO [main.py:415]   - giongnu_1: <PERSON><PERSON><PERSON><PERSON> (vais1000)
2025-08-13 16:58:03,062 INFO [main.py:153] Loaded model: giongnam_1 - Ti<PERSON><PERSON> (vais1000)
2025-08-13 16:58:03,065 INFO [main.py:153] Loaded model: giongnu_1 - Ti<PERSON><PERSON> (vais1000)
2025-08-13 16:58:09,278 INFO [main.py:209] Created PiperVoice instance for model: giongnu_1
2025-08-13 16:58:09,278 INFO [main.py:336] Synthesizing text with model giongnu_1: Bạn biết không, khi tôi quyết định ăn kiêng, tôi đã cắt đường, cắt tinh bột, và cuối cùng chỉ còn lại món bánh pizza trong tâm trí
2025-08-13 16:58:09,827 INFO [main.py:358] TTS generated successfully: ef73cb32-1f14-4bdb-b20c-eac169bb1977 using model giongnu_1
2025-08-13 16:58:38,887 INFO [main.py:209] Created PiperVoice instance for model: giongnam_1
2025-08-13 16:58:38,887 INFO [main.py:336] Synthesizing text with model giongnam_1: Bạn biết không, khi tôi quyết định ăn kiêng, tôi đã cắt đường, cắt tinh bột, và cuối cùng chỉ còn lại món bánh pizza trong tâm trí
2025-08-13 16:58:39,365 INFO [main.py:358] TTS generated successfully: c49e6b1d-56a9-4703-a915-f2b2149edfc9 using model giongnam_1
2025-08-13 17:01:12,351 INFO [main.py:336] Synthesizing text with model giongnam_1: Bạn biết không, khi tôi quyết định ăn kiêng, tôi đã cắt đường, cắt tinh bột, và cuối cùng chỉ còn lại món bánh pizza trong tâm trí! Thế là kiêng cử thất bại!
2025-08-13 17:01:12,976 INFO [main.py:358] TTS generated successfully: 9d25beba-e802-42b5-a57d-0a7dc8df972b using model giongnam_1
2025-08-13 17:01:31,806 INFO [main.py:336] Synthesizing text with model giongnam_1: Bạn biết không, khi tôi quyết định ăn kiêng, tôi đã cắt đường, cắt tinh bột, và cuối cùng chỉ còn lại món bánh pizza trong tâm trí... Thế là kiêng cử thất bại.
2025-08-13 17:01:32,450 INFO [main.py:358] TTS generated successfully: 836b38e9-cd62-4098-a2eb-83a7c2321754 using model giongnam_1
2025-08-13 17:01:52,581 INFO [main.py:336] Synthesizing text with model giongnam_1: Bạn biết không, khi tôi quyết định ăn kiêng, tôi đã cắt đường, cắt tinh bột, và cuối cùng chỉ còn lại món bánh pizza trong tâm trí ... Thế là kiêng cử thất bại.
2025-08-13 17:01:53,159 INFO [main.py:358] TTS generated successfully: 449d54de-fc8f-4044-b437-51d6544f8175 using model giongnam_1
2025-08-13 17:02:42,799 INFO [main.py:336] Synthesizing text with model giongnam_1: Bạn biết không, khi tôi quyết định ăn kiêng, tôi đã cắt đường, cắt tinh bột, và cuối cùng chỉ còn lại món bánh pizza trong tâm trí. Hahaha. Thế là kiêng cử thất bại.
2025-08-13 17:02:43,446 INFO [main.py:358] TTS generated successfully: 6013e138-b6fc-403a-9529-110e503a2128 using model giongnam_1
2025-08-13 17:03:03,371 INFO [main.py:336] Synthesizing text with model giongnam_1: Bạn biết không, khi tôi quyết định ăn kiêng, tôi đã cắt đường, cắt tinh bột, và cuối cùng chỉ còn lại món bánh pizza trong tâm trí. Ha ha ha. Thế là kiêng cử thất bại.
2025-08-13 17:03:03,976 INFO [main.py:358] TTS generated successfully: eda34d16-2bc9-424d-8ac8-7f13ce6c1ebe using model giongnam_1
2025-08-13 17:03:41,545 INFO [main.py:336] Synthesizing text with model giongnam_1: Bạn biết không, khi tôi quyết định ăn kiêng, tôi đã cắt đường, cắt tinh bột, và cuối cùng chỉ còn lại món bánh pizza trong tâm trí. < cười to >. Thế là kiêng cử thất bại.
2025-08-13 17:03:42,170 INFO [main.py:358] TTS generated successfully: e0bbcef2-3566-4227-a212-a2e927735320 using model giongnam_1
2025-08-13 17:06:24,127 INFO [main.py:336] Synthesizing text with model giongnam_1: <speak>Bạn biết không, khi tôi quyết định ăn kiêng, tôi đã cắt đường, cắt tinh bột, và cuối cùng chỉ còn lại món bánh pizza trong tâm trí. <say-as interpret-as="exclamation">{smile}</say-as>. Thế là kiêng cử thất bại.</speak>
2025-08-13 17:06:24,915 INFO [main.py:358] TTS generated successfully: 488276d5-dba0-4345-b598-211bc3f91bd5 using model giongnam_1
