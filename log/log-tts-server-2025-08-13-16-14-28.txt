2025-08-13 16:14:28,441 INFO [main.py:153] Loaded model: giongnam_1 - <PERSON><PERSON><PERSON><PERSON> (vais1000)
2025-08-13 16:14:28,442 INFO [main.py:153] Loaded model: giongnu_1 - <PERSON><PERSON><PERSON><PERSON> (vais1000)
2025-08-13 16:14:28,468 INFO [main.py:413] Loaded 2 Piper model(s)
2025-08-13 16:14:28,468 INFO [main.py:415]   - giongnam_1: <PERSON><PERSON><PERSON><PERSON> (vais1000)
2025-08-13 16:14:28,468 INFO [main.py:415]   - giongnu_1: <PERSON><PERSON><PERSON><PERSON> (vais1000)
2025-08-13 16:14:28,481 INFO [main.py:153] Loaded model: giongnam_1 - T<PERSON><PERSON><PERSON> (vais1000)
2025-08-13 16:14:28,481 INFO [main.py:153] Loaded model: giongnu_1 - Ti<PERSON><PERSON> (vais1000)
2025-08-13 16:14:34,533 INFO [main.py:209] Created PiperVoice instance for model: giongnu_1
2025-08-13 16:14:34,533 INFO [main.py:336] Synthesizing text with model giongnu_1: Xin chào các bạn
2025-08-13 16:14:34,803 INFO [main.py:358] TTS generated successfully: 346b7c6d-5d4f-41e5-bee6-17f8db95ab26 using model giongnu_1
2025-08-13 16:18:05,443 INFO [main.py:336] Synthesizing text with model giongnu_1: Bạn biết không, khi tôi quyết định ăn kiêng, tôi đã cắt đường, cắt tinh bột, và cuối cùng chỉ còn lại món bánh pizza trong tâm trí! Thế là kiêng cử thất bại!
2025-08-13 16:18:06,151 INFO [main.py:358] TTS generated successfully: b10870af-e564-4d59-8044-24b799f433a5 using model giongnu_1
2025-08-13 16:18:29,760 INFO [main.py:209] Created PiperVoice instance for model: giongnam_1
2025-08-13 16:18:29,760 INFO [main.py:336] Synthesizing text with model giongnam_1: Bạn biết không, khi tôi quyết định ăn kiêng, tôi đã cắt đường, cắt tinh bột, và cuối cùng chỉ còn lại món bánh pizza trong tâm trí! Thế là kiêng cử thất bại!
2025-08-13 16:18:30,350 INFO [main.py:358] TTS generated successfully: 0840a847-6a74-4ede-ac82-bb1fea8847a6 using model giongnam_1
