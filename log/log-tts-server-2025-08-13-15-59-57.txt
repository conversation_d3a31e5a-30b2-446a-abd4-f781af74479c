2025-08-13 15:59:57,633 INFO [main.py:153] Loaded model: giongnam_1 - <PERSON><PERSON><PERSON><PERSON> (vais1000)
2025-08-13 15:59:57,633 INFO [main.py:153] Loaded model: giongnu_1 - <PERSON><PERSON><PERSON><PERSON> (vais1000)
2025-08-13 15:59:57,663 INFO [main.py:397] Loaded 2 Piper model(s)
2025-08-13 15:59:57,663 INFO [main.py:399]   - giongnam_1: <PERSON><PERSON><PERSON><PERSON> (vais1000)
2025-08-13 15:59:57,663 INFO [main.py:399]   - giongnu_1: <PERSON><PERSON><PERSON><PERSON> (vais1000)
2025-08-13 15:59:57,674 INFO [main.py:153] Loaded model: giongnam_1 - T<PERSON><PERSON><PERSON> Vi<PERSON> (vais1000)
2025-08-13 15:59:57,674 INFO [main.py:153] Loaded model: giongnu_1 - <PERSON><PERSON><PERSON><PERSON> (vais1000)
2025-08-13 16:00:14,740 INFO [main.py:198] Created PiperVoice instance for model: giongnam_1
2025-08-13 16:00:14,741 ERROR [main.py:354] TTS generation failed:
Traceback (most recent call last):
  File "/Users/<USER>/projects/vetc/robotics/TTS/main.py", line 314, in text_to_speech
    audio_data = voice.synthesize(
TypeError: synthesize() got an unexpected keyword argument 'speaker_id'

