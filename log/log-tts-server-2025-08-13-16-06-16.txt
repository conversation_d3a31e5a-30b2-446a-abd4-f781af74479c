2025-08-13 16:06:16,802 INFO [main.py:153] Loaded model: giongnam_1 - T<PERSON><PERSON><PERSON> (vais1000)
2025-08-13 16:06:16,802 INFO [main.py:153] Loaded model: giongnu_1 - <PERSON><PERSON><PERSON><PERSON> (vais1000)
2025-08-13 16:06:16,828 INFO [main.py:407] Loaded 2 Piper model(s)
2025-08-13 16:06:16,828 INFO [main.py:409]   - giongnam_1: <PERSON><PERSON><PERSON><PERSON> (vais1000)
2025-08-13 16:06:16,828 INFO [main.py:409]   - giongnu_1: <PERSON><PERSON><PERSON><PERSON> (vais1000)
2025-08-13 16:06:16,840 INFO [main.py:153] Loaded model: giongnam_1 - T<PERSON><PERSON><PERSON> (vais1000)
2025-08-13 16:06:16,840 INFO [main.py:153] Loaded model: giongnu_1 - Ti<PERSON><PERSON> (vais1000)
2025-08-13 16:06:20,220 INFO [main.py:209] Created PiperVoice instance for model: giongnam_1
2025-08-13 16:06:20,220 INFO [main.py:329] Synthesizing text with model giongnam_1: Xin chào các bạn
2025-08-13 16:06:59,988 INFO [main.py:353] TTS generated successfully: 25c13baa-631d-4bba-9cfe-09debfae3145 using model giongnam_1
2025-08-13 16:08:00,189 INFO [main.py:329] Synthesizing text with model giongnam_1: Xin chào các bạn
2025-08-13 16:08:00,520 INFO [main.py:353] TTS generated successfully: 66c2436f-30d2-40eb-aa09-b3e94338e0ad using model giongnam_1
2025-08-13 16:08:14,528 INFO [main.py:209] Created PiperVoice instance for model: giongnu_1
2025-08-13 16:08:14,528 INFO [main.py:329] Synthesizing text with model giongnu_1: Xin chào các bạn
2025-08-13 16:08:14,731 INFO [main.py:353] TTS generated successfully: 8b254345-dd38-4633-803f-92ab361bb039 using model giongnu_1
2025-08-13 16:08:29,540 INFO [main.py:329] Synthesizing text with model giongnu_1: Xin chào các bạn
2025-08-13 16:08:29,765 INFO [main.py:353] TTS generated successfully: f2ce4dad-4a49-450a-bdcb-8c6259ccbdbb using model giongnu_1
