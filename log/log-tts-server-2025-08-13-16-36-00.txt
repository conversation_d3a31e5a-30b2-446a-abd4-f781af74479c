2025-08-13 16:36:00,864 INFO [main.py:153] Loaded model: giongnam_1 - Ti<PERSON><PERSON> (vais1000)
2025-08-13 16:36:00,864 INFO [main.py:153] Loaded model: giongnu_1 - <PERSON><PERSON><PERSON><PERSON> (vais1000)
2025-08-13 16:36:00,911 INFO [main.py:413] Loaded 2 Piper model(s)
2025-08-13 16:36:00,911 INFO [main.py:415]   - giongnam_1: Ti<PERSON><PERSON> (vais1000)
2025-08-13 16:36:00,911 INFO [main.py:415]   - giongnu_1: <PERSON><PERSON><PERSON><PERSON> (vais1000)
2025-08-13 16:36:00,921 INFO [main.py:153] Loaded model: giongnam_1 - Ti<PERSON><PERSON> (vais1000)
2025-08-13 16:36:00,921 INFO [main.py:153] Loaded model: giongnu_1 - Ti<PERSON><PERSON> (vais1000)
2025-08-13 16:36:10,331 INFO [main.py:209] Created PiperVoice instance for model: giongnam_1
2025-08-13 16:36:10,331 INFO [main.py:336] Synthesizing text with model giongnam_1: Bạn biết không, khi tôi quyết định ăn kiêng, tôi đã cắt đường, cắt tinh bột, và cuối cùng chỉ còn lại món bánh pizza trong tâm trí! Thế là kiêng cử thất bại!
2025-08-13 16:36:11,065 INFO [main.py:358] TTS generated successfully: d5fce6d9-09ee-42e4-9291-9338aba15229 using model giongnam_1
2025-08-13 16:36:34,931 INFO [main.py:209] Created PiperVoice instance for model: giongnu_1
2025-08-13 16:36:34,931 INFO [main.py:336] Synthesizing text with model giongnu_1: Bạn biết không, khi tôi quyết định ăn kiêng, tôi đã cắt đường, cắt tinh bột, và cuối cùng chỉ còn lại món bánh pizza trong tâm trí
2025-08-13 16:36:35,431 INFO [main.py:358] TTS generated successfully: 43005d65-667f-40d3-9ecb-f4f42d581c61 using model giongnu_1
