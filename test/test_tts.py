from piper import PiperVoice
import numpy as np
from scipy.io.wavfile import write
from IPython.display import Audio

# 1. Đường dẫn đến mô hình
model_path = "../models/giongnam_1/model.onnx"

# 2. Tải mô hình
print("<PERSON>ang tải mô hình Piper...")
voice = PiperVoice.load(model_path)

# 2.1. <PERSON><PERSON>ều chỉnh tốc độ nói (length_scale)
# < 1.0: nói nhanh hơn (ví dụ: 0.8 = nhanh hơn 25%)
# = 1.0: tốc độ bình thường
# > 1.0: nói chậm hơn (ví dụ: 1.2 = chậm hơn 20%)
speed_scale = 1.1  # Thay đổi giá trị này để điều chỉnh tốc độ
voice.config.length_scale = speed_scale
print(f"🎛️  Tốc độ nói được đặt: {speed_scale} ({'nhanh hơn' if speed_scale < 1.0 else 'chậm hơn' if speed_scale > 1.0 else 'bình thường'})")

# 3. <PERSON><PERSON><PERSON> bản cần tổng hợp
text = "Trong thế kỷ 21, công nghệ đã trở thành một phần không thể thiếu trong cuộc sống hàng ngày của con người. Từ việc kết nối mọi người qua mạng xã hội đến việc tối ưu hóa quy trình làm việc trong các doanh nghiệp, công nghệ đã mang lại những lợi ích vượt trội. Tuy nhiên, bên cạnh những ưu điểm, chúng ta cũng phải đối mặt với nhiều thách thức"

# 4. Tổng hợp thành numpy array (không ghi file trực tiếp)
print("Đang tổng hợp giọng nói...")
# synthesize() trả về generator của AudioChunk objects
audio_chunks = list(voice.synthesize(text))

# Ghép các chunks lại và lấy sample rate từ chunk đầu tiên
if audio_chunks:
    sample_rate = audio_chunks[0].sample_rate
    audio_arrays = [chunk.audio_int16_array for chunk in audio_chunks]
    audio = np.concatenate(audio_arrays)
else:
    raise RuntimeError("Không có audio chunks được tạo!")

# 5. Ghi file bằng scipy (đảm bảo dữ liệu được ghi đúng)
output_wav = "output_run191.wav"
write(output_wav, sample_rate, audio)
print(f"✅ Tổng hợp thành công! File lưu tại: {output_wav}")

# 6. Phát trong Jupyter
Audio(output_wav)
